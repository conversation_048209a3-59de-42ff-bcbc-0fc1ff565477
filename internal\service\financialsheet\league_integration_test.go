package financialsheet

import (
	"context"
	"testing"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/dreamboard"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/model/gamification"
	"github.com/dsoplabs/dinbora-backend/internal/model/league"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

/*
Integration Tests for Financial Sheet Service with League Service

These tests verify that the actual financial sheet service functions
(CreateTransaction, CreateDreamTransaction, NoTransaction) properly call
the RecordTransactionForAllUserLeagues function with correct parameters.

This ensures the integration between the financial sheet service and
league service is working correctly by testing the real function calls.
*/

// MockLeagueServiceForIntegration implements the league service interface for integration testing
type MockLeagueServiceForIntegration struct {
	mock.Mock
}

func (m *MockLeagueServiceForIntegration) RecordTransactionForAllUserLeagues(ctx context.Context, userID string, transactionDate time.Time) error {
	args := m.Called(ctx, userID, transactionDate)
	return args.Error(0)
}

// Implement other required methods as no-ops for testing
func (m *MockLeagueServiceForIntegration) CreateLeague(ctx context.Context, ownerUserID string, ownerUserName string, ownerPhotoURL string, leagueName string, startDate time.Time, endDate time.Time) (*league.League, error) {
	return nil, nil
}

func (m *MockLeagueServiceForIntegration) FindLeague(ctx context.Context, leagueID string, userID string) (*league.League, error) {
	return nil, nil
}

func (m *MockLeagueServiceForIntegration) FindAllLeagues(ctx context.Context, userID string) ([]*league.League, error) {
	return nil, nil
}

func (m *MockLeagueServiceForIntegration) PatchLeague(ctx context.Context, leagueID string, userID string, name *string) (*league.League, error) {
	return nil, nil
}

func (m *MockLeagueServiceForIntegration) DeleteLeague(ctx context.Context, leagueID string, userID string) error {
	return nil
}

func (m *MockLeagueServiceForIntegration) InviteDetails(ctx context.Context, code string) (*league.League, error) {
	return nil, nil
}

func (m *MockLeagueServiceForIntegration) JoinLeague(ctx context.Context, userID string, userName string, photoURL string, inviteCode string) (*league.League, error) {
	return nil, nil
}

func (m *MockLeagueServiceForIntegration) LeaveLeague(ctx context.Context, leagueID string, userID string) error {
	return nil
}

func (m *MockLeagueServiceForIntegration) StartNewSeasonInLeague(ctx context.Context, leagueID string, userID string, startDate time.Time, endDate time.Time) (*league.League, error) {
	return nil, nil
}

func (m *MockLeagueServiceForIntegration) FindLeagueRanking(ctx context.Context, leagueID string, userID string) (*league.LeagueRanking, error) {
	return nil, nil
}

func (m *MockLeagueServiceForIntegration) FindLeagueCard(ctx context.Context, leagueID string, userID string) (*league.LeagueCard, error) {
	return nil, nil
}

func (m *MockLeagueServiceForIntegration) FindAllLeaguesCards(ctx context.Context, userID string) ([]*league.LeagueCard, error) {
	return nil, nil
}

// MockGamificationServiceForIntegration for integration testing
type MockGamificationServiceForIntegration struct {
	mock.Mock
}

func (m *MockGamificationServiceForIntegration) DetermineVisibleHeroesForUser(ctx context.Context, userID string) ([]*gamification.Hero, error) {
	return nil, nil
}

func (m *MockGamificationServiceForIntegration) FindUserAchievements(ctx context.Context, userID string) ([]*gamification.Achievement, error) {
	return nil, nil
}

func (m *MockGamificationServiceForIntegration) FindUsersAchievements(ctx context.Context, userIDs []string) (map[string][]*gamification.Achievement, error) {
	return nil, nil
}

func (m *MockGamificationServiceForIntegration) FindContentAchievements(ctx context.Context) ([]*content.Achievement, error) {
	return nil, nil
}

func (m *MockGamificationServiceForIntegration) CheckAchievements(ctx context.Context, userID string) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockGamificationServiceForIntegration) CheckDNAAchievement(ctx context.Context, userID string) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockGamificationServiceForIntegration) CheckExplorerAchievement(ctx context.Context, userID string) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockGamificationServiceForIntegration) CheckDreamAchievement(ctx context.Context, userID string) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockGamificationServiceForIntegration) CheckPlanningAchievement(ctx context.Context, userID string) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockGamificationServiceForIntegration) CheckInvestingAchievement(ctx context.Context, userID string) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

// MockDreamboardRepositoryForIntegration for integration testing
type MockDreamboardRepositoryForIntegration struct {
	mock.Mock
}

func (m *MockDreamboardRepositoryForIntegration) Find(ctx context.Context, id primitive.ObjectID) (*dreamboard.Dreamboard, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*dreamboard.Dreamboard), args.Error(1)
}

func (m *MockDreamboardRepositoryForIntegration) FindAll(ctx context.Context) ([]*dreamboard.Dreamboard, error) {
	args := m.Called(ctx)
	return args.Get(0).([]*dreamboard.Dreamboard), args.Error(1)
}

func (m *MockDreamboardRepositoryForIntegration) FindAllByUsers(ctx context.Context, userIDs []string) ([]*dreamboard.Dreamboard, error) {
	args := m.Called(ctx, userIDs)
	return args.Get(0).([]*dreamboard.Dreamboard), args.Error(1)
}

func (m *MockDreamboardRepositoryForIntegration) FindByUser(ctx context.Context, userID string) (*dreamboard.Dreamboard, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*dreamboard.Dreamboard), args.Error(1)
}

func (m *MockDreamboardRepositoryForIntegration) FindByDreamID(ctx context.Context, dreamID primitive.ObjectID) (*dreamboard.Dreamboard, error) {
	args := m.Called(ctx, dreamID)
	return args.Get(0).(*dreamboard.Dreamboard), args.Error(1)
}

func (m *MockDreamboardRepositoryForIntegration) FindCategory(ctx context.Context, boardID primitive.ObjectID, categoryID primitive.ObjectID) (*dreamboard.Category, error) {
	args := m.Called(ctx, boardID, categoryID)
	return args.Get(0).(*dreamboard.Category), args.Error(1)
}

func (m *MockDreamboardRepositoryForIntegration) FindDream(ctx context.Context, boardID primitive.ObjectID, dreamID primitive.ObjectID) (*dreamboard.Dream, error) {
	args := m.Called(ctx, boardID, dreamID)
	return args.Get(0).(*dreamboard.Dream), args.Error(1)
}

func (m *MockDreamboardRepositoryForIntegration) FindContribution(ctx context.Context, id primitive.ObjectID) (*dreamboard.Contribution, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*dreamboard.Contribution), args.Error(1)
}

func (m *MockDreamboardRepositoryForIntegration) FindContributionsByDreamID(ctx context.Context, dreamID string) ([]*dreamboard.Contribution, error) {
	args := m.Called(ctx, dreamID)
	return args.Get(0).([]*dreamboard.Contribution), args.Error(1)
}

func (m *MockDreamboardRepositoryForIntegration) FindContributionsByUserID(ctx context.Context, userID string) ([]*dreamboard.Contribution, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]*dreamboard.Contribution), args.Error(1)
}

func (m *MockDreamboardRepositoryForIntegration) FindContributionByDreamAndUser(ctx context.Context, dreamID, userID string) (*dreamboard.Contribution, error) {
	args := m.Called(ctx, dreamID, userID)
	return args.Get(0).(*dreamboard.Contribution), args.Error(1)
}

func (m *MockDreamboardRepositoryForIntegration) FindActiveContributionsByDreamID(ctx context.Context, dreamID string) ([]*dreamboard.Contribution, error) {
	args := m.Called(ctx, dreamID)
	return args.Get(0).([]*dreamboard.Contribution), args.Error(1)
}

func (m *MockDreamboardRepositoryForIntegration) Create(ctx context.Context, dreamboard *dreamboard.Dreamboard) (string, error) {
	args := m.Called(ctx, dreamboard)
	return args.String(0), args.Error(1)
}

func (m *MockDreamboardRepositoryForIntegration) CreateDelete(ctx context.Context, deletedDreamboard *dreamboard.DeletedDreamboard) error {
	args := m.Called(ctx, deletedDreamboard)
	return args.Error(0)
}

func (m *MockDreamboardRepositoryForIntegration) Update(ctx context.Context, dreamboard *dreamboard.Dreamboard) error {
	args := m.Called(ctx, dreamboard)
	return args.Error(0)
}

func (m *MockDreamboardRepositoryForIntegration) Delete(ctx context.Context, id primitive.ObjectID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockDreamboardRepositoryForIntegration) CreateCategory(ctx context.Context, boardID primitive.ObjectID, category *dreamboard.Category) error {
	args := m.Called(ctx, boardID, category)
	return args.Error(0)
}

func (m *MockDreamboardRepositoryForIntegration) CreateCategories(ctx context.Context, boardID primitive.ObjectID, categories []dreamboard.Category) error {
	args := m.Called(ctx, boardID, categories)
	return args.Error(0)
}

func (m *MockDreamboardRepositoryForIntegration) UpdateCategory(ctx context.Context, boardID primitive.ObjectID, category *dreamboard.Category) error {
	args := m.Called(ctx, boardID, category)
	return args.Error(0)
}

func (m *MockDreamboardRepositoryForIntegration) DeleteCategory(ctx context.Context, boardID primitive.ObjectID, categoryID primitive.ObjectID) error {
	args := m.Called(ctx, boardID, categoryID)
	return args.Error(0)
}

func (m *MockDreamboardRepositoryForIntegration) CreateDream(ctx context.Context, boardID primitive.ObjectID, dream *dreamboard.Dream) error {
	args := m.Called(ctx, boardID, dream)
	return args.Error(0)
}

func (m *MockDreamboardRepositoryForIntegration) UpdateDream(ctx context.Context, boardID primitive.ObjectID, dream *dreamboard.Dream) error {
	args := m.Called(ctx, boardID, dream)
	return args.Error(0)
}

func (m *MockDreamboardRepositoryForIntegration) RemoveDream(ctx context.Context, boardID primitive.ObjectID, dreamID primitive.ObjectID) error {
	args := m.Called(ctx, boardID, dreamID)
	return args.Error(0)
}

func (m *MockDreamboardRepositoryForIntegration) CreateShareLink(ctx context.Context, shareLink *dreamboard.ShareLink) (string, error) {
	args := m.Called(ctx, shareLink)
	return args.String(0), args.Error(1)
}

func (m *MockDreamboardRepositoryForIntegration) UpdateShareLink(ctx context.Context, shareLink *dreamboard.ShareLink) error {
	args := m.Called(ctx, shareLink)
	return args.Error(0)
}

func (m *MockDreamboardRepositoryForIntegration) DeleteShareLink(ctx context.Context, id primitive.ObjectID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockDreamboardRepositoryForIntegration) CreateContribution(ctx context.Context, contribution *dreamboard.Contribution) (string, error) {
	args := m.Called(ctx, contribution)
	return args.String(0), args.Error(1)
}

func (m *MockDreamboardRepositoryForIntegration) UpdateContribution(ctx context.Context, contribution *dreamboard.Contribution) error {
	args := m.Called(ctx, contribution)
	return args.Error(0)
}

func (m *MockDreamboardRepositoryForIntegration) UpdateContributionStatusByDreamID(ctx context.Context, dreamID string, status dreamboard.ContributionStatus) error {
	args := m.Called(ctx, dreamID, status)
	return args.Error(0)
}

func (m *MockDreamboardRepositoryForIntegration) DeleteContribution(ctx context.Context, id primitive.ObjectID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}
