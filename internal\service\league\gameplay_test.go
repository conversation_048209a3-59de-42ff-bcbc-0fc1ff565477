package league

import (
	"context"
	"testing"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/league"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

/*
Comprehensive Test Suite for RecordTransactionForAllUserLeagues Function

This test suite validates the behavior of the RecordTransactionForAllUserLeagues function
which is called by all three transaction functions from the financial sheet service:
- CreateTransaction
- CreateDreamTransaction
- NoTransaction

Test Coverage:
1. Multi-league scenario with different initial states and level progressions
2. Individual transaction type scenarios (Create, Dream, NoTransaction)
3. First transaction ever (zero streak, no last activity date)
4. Level progression across different league requirements
5. Edge cases: empty leagues list, same-day transactions, user not in league
6. Error handling: repository errors, update failures

Key Validations:
- Streak counters are properly incremented for each league
- Level progression is calculated correctly based on individual league requirements
- Transaction dates are properly handled and stored
- Same-day transactions are skipped (as per business logic)
- Error scenarios are handled gracefully
- Multiple leagues are processed independently and correctly
*/

// MockRepository implements the league repository interface for testing
type MockRepository struct {
	mock.Mock
}

func (m *MockRepository) Find(ctx context.Context, id primitive.ObjectID) (*league.League, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*league.League), args.Error(1)
}

func (m *MockRepository) FindAllLeagues(ctx context.Context, userID string) ([]*league.League, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]*league.League), args.Error(1)
}

func (m *MockRepository) FindByID(ctx context.Context, leagueID string) (*league.League, error) {
	args := m.Called(ctx, leagueID)
	return args.Get(0).(*league.League), args.Error(1)
}

func (m *MockRepository) FindByIDLoggedUser(ctx context.Context, leagueID string, userID string) (*league.League, error) {
	args := m.Called(ctx, leagueID, userID)
	return args.Get(0).(*league.League), args.Error(1)
}

func (m *MockRepository) FindByInviteCode(ctx context.Context, code string) (*league.League, error) {
	args := m.Called(ctx, code)
	return args.Get(0).(*league.League), args.Error(1)
}

func (m *MockRepository) Create(ctx context.Context, l *league.League) (string, error) {
	args := m.Called(ctx, l)
	return args.String(0), args.Error(1)
}

func (m *MockRepository) Update(ctx context.Context, l *league.League) error {
	args := m.Called(ctx, l)
	return args.Error(0)
}

func (m *MockRepository) Delete(ctx context.Context, id primitive.ObjectID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

// Helper function to create test leagues with different initial states
func createTestLeague(leagueID, name string, userID string, initialStreak int, lastActivityDate time.Time, levelRequirements []league.LevelRequirement) *league.League {
	// Set current level based on initial streak and level requirements
	currentLevel := league.BronzeLevel
	for _, req := range levelRequirements {
		if initialStreak >= req.TransactionStreakNeeded {
			currentLevel = req.Level
		}
	}

	return &league.League{
		ObjectID:        primitive.NewObjectID(),
		ID:              leagueID,
		Name:            name,
		BackgroundColor: "#FF0000",
		OwnerUserID:     "owner123",
		Members: []league.LeagueMember{
			{
				UserID:                 userID,
				UserName:               "Test User",
				PhotoURL:               "https://example.com/photo.jpg",
				JoinedAt:               time.Now().AddDate(0, 0, -30),
				TransactionStreak:      initialStreak,
				CurrentLeagueLevel:     currentLevel,
				LastStreakActivityDate: lastActivityDate,
			},
		},
		CurrentSeason: league.Season{
			StartDate: time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),   // Fixed season dates
			EndDate:   time.Date(2024, 12, 31, 0, 0, 0, 0, time.UTC), // to ensure transaction date is within season
		},
		LevelRequirements: levelRequirements,
	}
}

// Helper function to create default level requirements
func createDefaultLevelRequirements() []league.LevelRequirement {
	return []league.LevelRequirement{
		{Level: league.BronzeLevel, TransactionStreakNeeded: 0, Emoji: "🥉", Color: "#CD7F32", Description: "Bronze Tier"},
		{Level: league.SilverLevel, TransactionStreakNeeded: 30, Emoji: "🥈", Color: "#C0C0C0", Description: "Silver Tier"},
		{Level: league.GoldLevel, TransactionStreakNeeded: 60, Emoji: "🥇", Color: "#FFD700", Description: "Gold Tier"},
		{Level: league.DiamondLevel, TransactionStreakNeeded: 90, Emoji: "💎", Color: "#B9F2FF", Description: "Diamond Tier"},
	}
}

// Helper function to create custom level requirements for testing different progression scenarios
func createCustomLevelRequirements() []league.LevelRequirement {
	return []league.LevelRequirement{
		{Level: league.BronzeLevel, TransactionStreakNeeded: 0, Emoji: "🥉", Color: "#CD7F32", Description: "Bronze Tier"},
		{Level: league.SilverLevel, TransactionStreakNeeded: 5, Emoji: "🥈", Color: "#C0C0C0", Description: "Silver Tier"},
		{Level: league.GoldLevel, TransactionStreakNeeded: 10, Emoji: "🥇", Color: "#FFD700", Description: "Gold Tier"},
		{Level: league.DiamondLevel, TransactionStreakNeeded: 15, Emoji: "💎", Color: "#B9F2FF", Description: "Diamond Tier"},
	}
}

func TestRecordTransactionForAllUserLeagues_ComprehensiveScenario(t *testing.T) {
	// Arrange
	ctx := context.Background()
	userID := "test-user-123"
	transactionDate := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)

	mockRepo := &MockRepository{}
	service := &service{
		Repository: mockRepo,
	}

	// Create three leagues with different initial states
	// League 1: First transaction ever (zero streak, no last activity date)
	league1 := createTestLeague("league1", "Bronze League", userID, 0, time.Time{}, createDefaultLevelRequirements())

	// League 2: Continuing streak (29 -> 30, should reach Silver level)
	league2 := createTestLeague("league2", "Silver League", userID, 29, transactionDate.AddDate(0, 0, -2), createDefaultLevelRequirements())

	// League 3: Custom requirements (4 -> 5, should reach Silver level)
	league3 := createTestLeague("league3", "Custom League", userID, 4, transactionDate.AddDate(0, 0, -2), createCustomLevelRequirements())

	userLeagues := []*league.League{league1, league2, league3}

	// Mock repository expectations
	mockRepo.On("FindAllLeagues", ctx, userID).Return(userLeagues, nil)

	// Expect Update to be called for each league
	mockRepo.On("Update", ctx, mock.MatchedBy(func(l *league.League) bool {
		return l.ID == "league1"
	})).Return(nil)

	mockRepo.On("Update", ctx, mock.MatchedBy(func(l *league.League) bool {
		return l.ID == "league2"
	})).Return(nil)

	mockRepo.On("Update", ctx, mock.MatchedBy(func(l *league.League) bool {
		return l.ID == "league3"
	})).Return(nil)

	// Act
	err := service.RecordTransactionForAllUserLeagues(ctx, userID, transactionDate)

	// Assert
	assert.NoError(t, err)

	// Verify League 1 (first transaction ever)
	league1Member := league1.Members[0]
	assert.Equal(t, 1, league1Member.TransactionStreak, "League 1: First transaction should set streak to 1")
	assert.Equal(t, transactionDate, league1Member.LastStreakActivityDate, "League 1: Last activity date should be updated")
	assert.Equal(t, league.BronzeLevel, league1Member.CurrentLeagueLevel, "League 1: Should remain at Bronze level")

	// Verify League 2 (streak continuation, should reach Silver level)
	league2Member := league2.Members[0]
	assert.Equal(t, 30, league2Member.TransactionStreak, "League 2: Streak should increment from 29 to 30")
	assert.Equal(t, transactionDate, league2Member.LastStreakActivityDate, "League 2: Last activity date should be updated")
	assert.Equal(t, league.SilverLevel, league2Member.CurrentLeagueLevel, "League 2: Should advance to Silver level at 30 streak")

	// Verify League 3 (custom requirements, should reach Silver level)
	league3Member := league3.Members[0]
	assert.Equal(t, 5, league3Member.TransactionStreak, "League 3: Streak should increment from 4 to 5")
	assert.Equal(t, transactionDate, league3Member.LastStreakActivityDate, "League 3: Last activity date should be updated")
	assert.Equal(t, league.SilverLevel, league3Member.CurrentLeagueLevel, "League 3: Should advance to Silver level at 5 streak")

	mockRepo.AssertExpectations(t)
}

func TestRecordTransactionForAllUserLeagues_CreateTransaction_Scenario(t *testing.T) {
	// Test scenario simulating CreateTransaction call
	ctx := context.Background()
	userID := "test-user-123"
	transactionDate := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)

	mockRepo := &MockRepository{}
	service := &service{
		Repository: mockRepo,
	}

	// Create leagues with different starting streaks (use dates that are not same day)
	league1 := createTestLeague("league1", "League A", userID, 5, transactionDate.AddDate(0, 0, -2), createCustomLevelRequirements())
	league2 := createTestLeague("league2", "League B", userID, 14, transactionDate.AddDate(0, 0, -2), createCustomLevelRequirements())
	league3 := createTestLeague("league3", "League C", userID, 9, transactionDate.AddDate(0, 0, -2), createCustomLevelRequirements())

	userLeagues := []*league.League{league1, league2, league3}

	mockRepo.On("FindAllLeagues", ctx, userID).Return(userLeagues, nil)
	mockRepo.On("Update", ctx, mock.AnythingOfType("*league.League")).Return(nil).Times(3)

	// Act - Simulate CreateTransaction calling RecordTransactionForAllUserLeagues
	err := service.RecordTransactionForAllUserLeagues(ctx, userID, transactionDate)

	// Assert
	assert.NoError(t, err)

	// Verify all leagues had their streaks incremented
	assert.Equal(t, 6, league1.Members[0].TransactionStreak, "League 1: Streak should increment from 5 to 6")
	assert.Equal(t, league.SilverLevel, league1.Members[0].CurrentLeagueLevel, "League 1: Should advance to Silver at 6 streak")

	assert.Equal(t, 15, league2.Members[0].TransactionStreak, "League 2: Streak should increment from 14 to 15")
	assert.Equal(t, league.DiamondLevel, league2.Members[0].CurrentLeagueLevel, "League 2: Should advance to Diamond at 15 streak")

	assert.Equal(t, 10, league3.Members[0].TransactionStreak, "League 3: Streak should increment from 9 to 10")
	assert.Equal(t, league.GoldLevel, league3.Members[0].CurrentLeagueLevel, "League 3: Should advance to Gold at 10 streak")

	mockRepo.AssertExpectations(t)
}

func TestRecordTransactionForAllUserLeagues_CreateDreamTransaction_Scenario(t *testing.T) {
	// Test scenario simulating CreateDreamTransaction call
	ctx := context.Background()
	userID := "test-user-123"
	transactionDate := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)

	mockRepo := &MockRepository{}
	service := &service{
		Repository: mockRepo,
	}

	// Create leagues with different starting streaks
	league1 := createTestLeague("league1", "Dream League A", userID, 2, transactionDate.AddDate(0, 0, -2), createCustomLevelRequirements())
	league2 := createTestLeague("league2", "Dream League B", userID, 7, transactionDate.AddDate(0, 0, -2), createCustomLevelRequirements())
	league3 := createTestLeague("league3", "Dream League C", userID, 12, transactionDate.AddDate(0, 0, -2), createCustomLevelRequirements())

	userLeagues := []*league.League{league1, league2, league3}

	mockRepo.On("FindAllLeagues", ctx, userID).Return(userLeagues, nil)
	mockRepo.On("Update", ctx, mock.AnythingOfType("*league.League")).Return(nil).Times(3)

	// Act - Simulate CreateDreamTransaction calling RecordTransactionForAllUserLeagues
	err := service.RecordTransactionForAllUserLeagues(ctx, userID, transactionDate)

	// Assert
	assert.NoError(t, err)

	// Verify all leagues had their streaks incremented
	assert.Equal(t, 3, league1.Members[0].TransactionStreak, "League 1: Streak should increment from 2 to 3")
	assert.Equal(t, league.BronzeLevel, league1.Members[0].CurrentLeagueLevel, "League 1: Should remain at Bronze")

	assert.Equal(t, 8, league2.Members[0].TransactionStreak, "League 2: Streak should increment from 7 to 8")
	assert.Equal(t, league.SilverLevel, league2.Members[0].CurrentLeagueLevel, "League 2: Should remain at Silver")

	assert.Equal(t, 13, league3.Members[0].TransactionStreak, "League 3: Streak should increment from 12 to 13")
	assert.Equal(t, league.GoldLevel, league3.Members[0].CurrentLeagueLevel, "League 3: Should remain at Gold")

	mockRepo.AssertExpectations(t)
}

func TestRecordTransactionForAllUserLeagues_NoTransaction_Scenario(t *testing.T) {
	// Test scenario simulating NoTransactions call
	ctx := context.Background()
	userID := "test-user-123"
	transactionDate := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)

	mockRepo := &MockRepository{}
	service := &service{
		Repository: mockRepo,
	}

	// Create leagues with different starting streaks
	league1 := createTestLeague("league1", "No-Trans League A", userID, 1, transactionDate.AddDate(0, 0, -2), createCustomLevelRequirements())
	league2 := createTestLeague("league2", "No-Trans League B", userID, 8, transactionDate.AddDate(0, 0, -2), createCustomLevelRequirements())
	league3 := createTestLeague("league3", "No-Trans League C", userID, 11, transactionDate.AddDate(0, 0, -2), createCustomLevelRequirements())

	userLeagues := []*league.League{league1, league2, league3}

	mockRepo.On("FindAllLeagues", ctx, userID).Return(userLeagues, nil)
	mockRepo.On("Update", ctx, mock.AnythingOfType("*league.League")).Return(nil).Times(3)

	// Act - Simulate NoTransactions calling RecordTransactionForAllUserLeagues
	err := service.RecordTransactionForAllUserLeagues(ctx, userID, transactionDate)

	// Assert
	assert.NoError(t, err)

	// Verify all leagues had their streaks incremented
	assert.Equal(t, 2, league1.Members[0].TransactionStreak, "League 1: Streak should increment from 1 to 2")
	assert.Equal(t, league.BronzeLevel, league1.Members[0].CurrentLeagueLevel, "League 1: Should remain at Bronze")

	assert.Equal(t, 9, league2.Members[0].TransactionStreak, "League 2: Streak should increment from 8 to 9")
	assert.Equal(t, league.SilverLevel, league2.Members[0].CurrentLeagueLevel, "League 2: Should remain at Silver")

	assert.Equal(t, 12, league3.Members[0].TransactionStreak, "League 3: Streak should increment from 11 to 12")
	assert.Equal(t, league.GoldLevel, league3.Members[0].CurrentLeagueLevel, "League 3: Should remain at Gold")

	mockRepo.AssertExpectations(t)
}

func TestRecordTransactionForAllUserLeagues_FirstTransactionEver(t *testing.T) {
	// Test scenario where user has never made a transaction in any league
	ctx := context.Background()
	userID := "test-user-123"
	transactionDate := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)

	mockRepo := &MockRepository{}
	service := &service{
		Repository: mockRepo,
	}

	// Create leagues where user has zero streak and no last activity date
	league1 := createTestLeague("league1", "First Trans League A", userID, 0, time.Time{}, createDefaultLevelRequirements())
	league2 := createTestLeague("league2", "First Trans League B", userID, 0, time.Time{}, createDefaultLevelRequirements())
	league3 := createTestLeague("league3", "First Trans League C", userID, 0, time.Time{}, createDefaultLevelRequirements())

	userLeagues := []*league.League{league1, league2, league3}

	mockRepo.On("FindAllLeagues", ctx, userID).Return(userLeagues, nil)
	mockRepo.On("Update", ctx, mock.AnythingOfType("*league.League")).Return(nil).Times(3)

	// Act
	err := service.RecordTransactionForAllUserLeagues(ctx, userID, transactionDate)

	// Assert
	assert.NoError(t, err)

	// Verify all leagues had their streaks set to 1 (first transaction)
	assert.Equal(t, 1, league1.Members[0].TransactionStreak, "League 1: First transaction should set streak to 1")
	assert.Equal(t, transactionDate, league1.Members[0].LastStreakActivityDate, "League 1: Last activity date should be set")
	assert.Equal(t, league.BronzeLevel, league1.Members[0].CurrentLeagueLevel, "League 1: Should be at Bronze level")

	assert.Equal(t, 1, league2.Members[0].TransactionStreak, "League 2: First transaction should set streak to 1")
	assert.Equal(t, transactionDate, league2.Members[0].LastStreakActivityDate, "League 2: Last activity date should be set")
	assert.Equal(t, league.BronzeLevel, league2.Members[0].CurrentLeagueLevel, "League 2: Should be at Bronze level")

	assert.Equal(t, 1, league3.Members[0].TransactionStreak, "League 3: First transaction should set streak to 1")
	assert.Equal(t, transactionDate, league3.Members[0].LastStreakActivityDate, "League 3: Last activity date should be set")
	assert.Equal(t, league.BronzeLevel, league3.Members[0].CurrentLeagueLevel, "League 3: Should be at Bronze level")

	mockRepo.AssertExpectations(t)
}

func TestRecordTransactionForAllUserLeagues_LevelProgression(t *testing.T) {
	// Test level progression across multiple leagues with different requirements
	ctx := context.Background()
	userID := "test-user-123"
	transactionDate := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)

	mockRepo := &MockRepository{}
	service := &service{
		Repository: mockRepo,
	}

	// Create leagues with different level requirements and users at different progression points
	league1 := createTestLeague("league1", "Progression League A", userID, 4, transactionDate.AddDate(0, 0, -2), createCustomLevelRequirements())
	league2 := createTestLeague("league2", "Progression League B", userID, 9, transactionDate.AddDate(0, 0, -2), createCustomLevelRequirements())
	league3 := createTestLeague("league3", "Progression League C", userID, 14, transactionDate.AddDate(0, 0, -2), createCustomLevelRequirements())

	userLeagues := []*league.League{league1, league2, league3}

	mockRepo.On("FindAllLeagues", ctx, userID).Return(userLeagues, nil)
	mockRepo.On("Update", ctx, mock.AnythingOfType("*league.League")).Return(nil).Times(3)

	// Act
	err := service.RecordTransactionForAllUserLeagues(ctx, userID, transactionDate)

	// Assert
	assert.NoError(t, err)

	// Verify level progressions
	// League 1: 4 -> 5 (Bronze -> Silver at 5)
	assert.Equal(t, 5, league1.Members[0].TransactionStreak)
	assert.Equal(t, league.SilverLevel, league1.Members[0].CurrentLeagueLevel, "League 1: Should advance to Silver at 5 streak")

	// League 2: 9 -> 10 (Silver -> Gold at 10)
	assert.Equal(t, 10, league2.Members[0].TransactionStreak)
	assert.Equal(t, league.GoldLevel, league2.Members[0].CurrentLeagueLevel, "League 2: Should advance to Gold at 10 streak")

	// League 3: 14 -> 15 (Gold -> Diamond at 15)
	assert.Equal(t, 15, league3.Members[0].TransactionStreak)
	assert.Equal(t, league.DiamondLevel, league3.Members[0].CurrentLeagueLevel, "League 3: Should advance to Diamond at 15 streak")

	mockRepo.AssertExpectations(t)
}

func TestRecordTransactionForAllUserLeagues_EmptyLeaguesList(t *testing.T) {
	// Test scenario where user is not a member of any leagues
	ctx := context.Background()
	userID := "test-user-123"
	transactionDate := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)

	mockRepo := &MockRepository{}
	service := &service{
		Repository: mockRepo,
	}

	// Return empty leagues list
	userLeagues := []*league.League{}

	mockRepo.On("FindAllLeagues", ctx, userID).Return(userLeagues, nil)

	// Act
	err := service.RecordTransactionForAllUserLeagues(ctx, userID, transactionDate)

	// Assert
	assert.NoError(t, err, "Should handle empty leagues list gracefully")

	mockRepo.AssertExpectations(t)
}

func TestRecordTransactionForAllUserLeagues_RepositoryError(t *testing.T) {
	// Test error handling when repository fails
	ctx := context.Background()
	userID := "test-user-123"
	transactionDate := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)

	mockRepo := &MockRepository{}
	service := &service{
		Repository: mockRepo,
	}

	// Mock repository to return an error
	mockRepo.On("FindAllLeagues", ctx, userID).Return([]*league.League{}, assert.AnError)

	// Act
	err := service.RecordTransactionForAllUserLeagues(ctx, userID, transactionDate)

	// Assert
	assert.Error(t, err, "Should return error when repository fails")

	mockRepo.AssertExpectations(t)
}

func TestRecordTransactionForAllUserLeagues_SameDayTransaction(t *testing.T) {
	// Test scenario where user makes multiple transactions on the same day
	ctx := context.Background()
	userID := "test-user-123"
	transactionDate := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)

	mockRepo := &MockRepository{}
	service := &service{
		Repository: mockRepo,
	}

	// Create leagues where user already made a transaction today (same day should be skipped)
	league1 := createTestLeague("league1", "Same Day League A", userID, 5, transactionDate, createCustomLevelRequirements())
	league2 := createTestLeague("league2", "Same Day League B", userID, 10, transactionDate, createCustomLevelRequirements())

	userLeagues := []*league.League{league1, league2}

	mockRepo.On("FindAllLeagues", ctx, userID).Return(userLeagues, nil)
	// No Update should be called since same-day transactions are skipped

	// Act - Try to record another transaction on the same day
	err := service.RecordTransactionForAllUserLeagues(ctx, userID, transactionDate)

	// Assert
	assert.NoError(t, err)

	// Verify streaks are NOT incremented (same-day transactions are skipped)
	assert.Equal(t, 5, league1.Members[0].TransactionStreak, "League 1: Streak should NOT increment on same day")
	assert.Equal(t, 10, league2.Members[0].TransactionStreak, "League 2: Streak should NOT increment on same day")

	mockRepo.AssertExpectations(t)
}

func TestRecordTransactionForAllUserLeagues_UserNotInLeague(t *testing.T) {
	// Test scenario where leagues exist but user is not a member
	ctx := context.Background()
	userID := "test-user-123"
	transactionDate := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)

	mockRepo := &MockRepository{}
	service := &service{
		Repository: mockRepo,
	}

	// Create a league with a different user
	league1 := createTestLeague("league1", "Other User League", "other-user-456", 5, transactionDate.AddDate(0, 0, -1), createCustomLevelRequirements())

	userLeagues := []*league.League{league1}

	mockRepo.On("FindAllLeagues", ctx, userID).Return(userLeagues, nil)
	// No Update should be called since user is not a member

	// Act
	err := service.RecordTransactionForAllUserLeagues(ctx, userID, transactionDate)

	// Assert
	assert.NoError(t, err, "Should handle gracefully when user is not a member")

	// Verify no changes were made to the league
	assert.Equal(t, 5, league1.Members[0].TransactionStreak, "Other user's streak should remain unchanged")
	assert.Equal(t, "other-user-456", league1.Members[0].UserID, "Should be the other user")

	mockRepo.AssertExpectations(t)
}

func TestRecordTransactionForAllUserLeagues_UpdateError(t *testing.T) {
	// Test error handling when update fails for one league
	ctx := context.Background()
	userID := "test-user-123"
	transactionDate := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)

	mockRepo := &MockRepository{}
	service := &service{
		Repository: mockRepo,
	}

	// Create leagues
	league1 := createTestLeague("league1", "Update Error League A", userID, 5, transactionDate.AddDate(0, 0, -2), createCustomLevelRequirements())
	league2 := createTestLeague("league2", "Update Error League B", userID, 10, transactionDate.AddDate(0, 0, -2), createCustomLevelRequirements())

	userLeagues := []*league.League{league1, league2}

	mockRepo.On("FindAllLeagues", ctx, userID).Return(userLeagues, nil)

	// First update succeeds, second fails
	mockRepo.On("Update", ctx, mock.MatchedBy(func(l *league.League) bool {
		return l.ID == "league1"
	})).Return(nil)

	mockRepo.On("Update", ctx, mock.MatchedBy(func(l *league.League) bool {
		return l.ID == "league2"
	})).Return(assert.AnError)

	// Act
	err := service.RecordTransactionForAllUserLeagues(ctx, userID, transactionDate)

	// Assert
	assert.NoError(t, err, "Function should not return error even if individual updates fail")

	// Verify streaks were still incremented in memory
	assert.Equal(t, 6, league1.Members[0].TransactionStreak, "League 1: Streak should be incremented")
	assert.Equal(t, 11, league2.Members[0].TransactionStreak, "League 2: Streak should be incremented")

	mockRepo.AssertExpectations(t)
}
