package league

import (
	"context"
	"testing"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/dreamboard"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/model/gamification"
	"github.com/dsoplabs/dinbora-backend/internal/model/league"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	financialsheetService "github.com/dsoplabs/dinbora-backend/internal/service/financialsheet"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

/*
Integration Tests for League Service with Financial Sheet Service

These tests verify that the actual financial sheet service functions
(CreateTransaction, CreateDreamTransaction, NoTransaction) properly call
the RecordTransactionForAllUserLeagues function with correct parameters.

This ensures the integration between the financial sheet service and
league service is working correctly.
*/

// MockFinancialSheetRepository for integration testing
type MockFinancialSheetRepository struct {
	mock.Mock
}

func (m *MockFinancialSheetRepository) Find(ctx context.Context, id primitive.ObjectID) (*financialsheet.Record, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*financialsheet.Record), args.Error(1)
}

func (m *MockFinancialSheetRepository) FindAll(ctx context.Context) ([]*financialsheet.Record, error) {
	args := m.Called(ctx)
	return args.Get(0).([]*financialsheet.Record), args.Error(1)
}

func (m *MockFinancialSheetRepository) FindByUser(ctx context.Context, userID string) (*financialsheet.Record, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*financialsheet.Record), args.Error(1)
}

func (m *MockFinancialSheetRepository) FindByUsers(ctx context.Context, userIDs []string) (map[string]*financialsheet.Record, error) {
	args := m.Called(ctx, userIDs)
	return args.Get(0).(map[string]*financialsheet.Record), args.Error(1)
}

func (m *MockFinancialSheetRepository) FindByUserAndPeriod(ctx context.Context, userID string, year, month int, flatten bool, planning bool) (*financialsheet.Record, error) {
	args := m.Called(ctx, userID, year, month, flatten, planning)
	return args.Get(0).(*financialsheet.Record), args.Error(1)
}

func (m *MockFinancialSheetRepository) Create(ctx context.Context, record *financialsheet.Record) (string, error) {
	args := m.Called(ctx, record)
	return args.String(0), args.Error(1)
}

func (m *MockFinancialSheetRepository) Update(ctx context.Context, record *financialsheet.Record) error {
	args := m.Called(ctx, record)
	return args.Error(0)
}

func (m *MockFinancialSheetRepository) Delete(ctx context.Context, id primitive.ObjectID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockFinancialSheetRepository) FindAllTransactions(ctx context.Context, userID string, categoryType financialsheet.CategoryType, year, month int) ([]*financialsheet.Transaction, error) {
	args := m.Called(ctx, userID, categoryType, year, month)
	return args.Get(0).([]*financialsheet.Transaction), args.Error(1)
}

func (m *MockFinancialSheetRepository) FindTransactionMonthsInRange(ctx context.Context, userID string, startDate, endDate time.Time) (map[string]bool, error) {
	args := m.Called(ctx, userID, startDate, endDate)
	return args.Get(0).(map[string]bool), args.Error(1)
}

func (m *MockFinancialSheetRepository) FindCategoryByUserAndIdentifier(ctx context.Context, userID string, identifier string) (*financialsheet.Category, error) {
	args := m.Called(ctx, userID, identifier)
	return args.Get(0).(*financialsheet.Category), args.Error(1)
}

func (m *MockFinancialSheetRepository) CountUserCategories(ctx context.Context, userID string) (int, error) {
	args := m.Called(ctx, userID)
	return args.Int(0), args.Error(1)
}

// MockDreamboardRepository for integration testing
type MockDreamboardRepository struct {
	mock.Mock
}

func (m *MockDreamboardRepository) Find(ctx context.Context, id primitive.ObjectID) (*dreamboard.Dreamboard, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*dreamboard.Dreamboard), args.Error(1)
}

func (m *MockDreamboardRepository) FindAll(ctx context.Context) ([]*dreamboard.Dreamboard, error) {
	args := m.Called(ctx)
	return args.Get(0).([]*dreamboard.Dreamboard), args.Error(1)
}

func (m *MockDreamboardRepository) FindAllByUsers(ctx context.Context, userIDs []string) ([]*dreamboard.Dreamboard, error) {
	args := m.Called(ctx, userIDs)
	return args.Get(0).([]*dreamboard.Dreamboard), args.Error(1)
}

func (m *MockDreamboardRepository) FindByUser(ctx context.Context, userID string) (*dreamboard.Dreamboard, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*dreamboard.Dreamboard), args.Error(1)
}

func (m *MockDreamboardRepository) FindByDreamID(ctx context.Context, dreamID primitive.ObjectID) (*dreamboard.Dreamboard, error) {
	args := m.Called(ctx, dreamID)
	return args.Get(0).(*dreamboard.Dreamboard), args.Error(1)
}

func (m *MockDreamboardRepository) FindCategory(ctx context.Context, boardID primitive.ObjectID, categoryID primitive.ObjectID) (*dreamboard.Category, error) {
	args := m.Called(ctx, boardID, categoryID)
	return args.Get(0).(*dreamboard.Category), args.Error(1)
}

func (m *MockDreamboardRepository) FindDream(ctx context.Context, boardID primitive.ObjectID, dreamID primitive.ObjectID) (*dreamboard.Dream, error) {
	args := m.Called(ctx, boardID, dreamID)
	return args.Get(0).(*dreamboard.Dream), args.Error(1)
}

func (m *MockDreamboardRepository) FindContribution(ctx context.Context, id primitive.ObjectID) (*dreamboard.Contribution, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*dreamboard.Contribution), args.Error(1)
}

func (m *MockDreamboardRepository) FindContributionsByDreamID(ctx context.Context, dreamID string) ([]*dreamboard.Contribution, error) {
	args := m.Called(ctx, dreamID)
	return args.Get(0).([]*dreamboard.Contribution), args.Error(1)
}

func (m *MockDreamboardRepository) FindContributionsByUserID(ctx context.Context, userID string) ([]*dreamboard.Contribution, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]*dreamboard.Contribution), args.Error(1)
}

func (m *MockDreamboardRepository) FindContributionByDreamAndUser(ctx context.Context, dreamID, userID string) (*dreamboard.Contribution, error) {
	args := m.Called(ctx, dreamID, userID)
	return args.Get(0).(*dreamboard.Contribution), args.Error(1)
}

func (m *MockDreamboardRepository) FindActiveContributionsByDreamID(ctx context.Context, dreamID string) ([]*dreamboard.Contribution, error) {
	args := m.Called(ctx, dreamID)
	return args.Get(0).([]*dreamboard.Contribution), args.Error(1)
}

func (m *MockDreamboardRepository) Create(ctx context.Context, dreamboard *dreamboard.Dreamboard) (string, error) {
	args := m.Called(ctx, dreamboard)
	return args.String(0), args.Error(1)
}

func (m *MockDreamboardRepository) CreateDelete(ctx context.Context, deletedDreamboard *dreamboard.DeletedDreamboard) error {
	args := m.Called(ctx, deletedDreamboard)
	return args.Error(0)
}

func (m *MockDreamboardRepository) Update(ctx context.Context, dreamboard *dreamboard.Dreamboard) error {
	args := m.Called(ctx, dreamboard)
	return args.Error(0)
}

func (m *MockDreamboardRepository) Delete(ctx context.Context, id primitive.ObjectID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockDreamboardRepository) CreateCategory(ctx context.Context, boardID primitive.ObjectID, category *dreamboard.Category) error {
	args := m.Called(ctx, boardID, category)
	return args.Error(0)
}

func (m *MockDreamboardRepository) CreateCategories(ctx context.Context, boardID primitive.ObjectID, categories []dreamboard.Category) error {
	args := m.Called(ctx, boardID, categories)
	return args.Error(0)
}

func (m *MockDreamboardRepository) UpdateCategory(ctx context.Context, boardID primitive.ObjectID, category *dreamboard.Category) error {
	args := m.Called(ctx, boardID, category)
	return args.Error(0)
}

func (m *MockDreamboardRepository) DeleteCategory(ctx context.Context, boardID primitive.ObjectID, categoryID primitive.ObjectID) error {
	args := m.Called(ctx, boardID, categoryID)
	return args.Error(0)
}

func (m *MockDreamboardRepository) CreateDream(ctx context.Context, boardID primitive.ObjectID, dream *dreamboard.Dream) error {
	args := m.Called(ctx, boardID, dream)
	return args.Error(0)
}

func (m *MockDreamboardRepository) UpdateDream(ctx context.Context, boardID primitive.ObjectID, dream *dreamboard.Dream) error {
	args := m.Called(ctx, boardID, dream)
	return args.Error(0)
}

func (m *MockDreamboardRepository) RemoveDream(ctx context.Context, boardID primitive.ObjectID, dreamID primitive.ObjectID) error {
	args := m.Called(ctx, boardID, dreamID)
	return args.Error(0)
}

func (m *MockDreamboardRepository) CreateShareLink(ctx context.Context, shareLink *dreamboard.ShareLink) (string, error) {
	args := m.Called(ctx, shareLink)
	return args.String(0), args.Error(1)
}

func (m *MockDreamboardRepository) UpdateShareLink(ctx context.Context, shareLink *dreamboard.ShareLink) error {
	args := m.Called(ctx, shareLink)
	return args.Error(0)
}

func (m *MockDreamboardRepository) DeleteShareLink(ctx context.Context, id primitive.ObjectID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockDreamboardRepository) CreateContribution(ctx context.Context, contribution *dreamboard.Contribution) (string, error) {
	args := m.Called(ctx, contribution)
	return args.String(0), args.Error(1)
}

func (m *MockDreamboardRepository) UpdateContribution(ctx context.Context, contribution *dreamboard.Contribution) error {
	args := m.Called(ctx, contribution)
	return args.Error(0)
}

func (m *MockDreamboardRepository) UpdateContributionStatusByDreamID(ctx context.Context, dreamID string, status dreamboard.ContributionStatus) error {
	args := m.Called(ctx, dreamID, status)
	return args.Error(0)
}

func (m *MockDreamboardRepository) DeleteContribution(ctx context.Context, id primitive.ObjectID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

// MockGamificationService for integration testing
type MockGamificationService struct {
	mock.Mock
}

func (m *MockGamificationService) CheckAchievements(ctx context.Context, userID string) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockGamificationService) FindUserAchievements(ctx context.Context, userID string) ([]*gamification.Achievement, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]*gamification.Achievement), args.Error(1)
}

// Helper function to create a test financial sheet record
func createTestFinancialRecord(userID string) *financialsheet.Record {
	return &financialsheet.Record{
		ObjectID: primitive.NewObjectID(),
		UserID:   userID,
		UserName: "Test User",
		Balance:  monetary.Amount(0),
		Points: financialsheet.Points{
			Current:             0,
			Best:                0,
			LastTransactionDate: time.Time{},
		},
		YearData: make(map[int]financialsheet.YearData),
	}
}

// Helper function to create test leagues for integration tests
func createTestLeaguesForIntegration(userID string) []*league.League {
	return []*league.League{
		{
			ObjectID:        primitive.NewObjectID(),
			ID:              "league1",
			Name:            "Integration League 1",
			BackgroundColor: "#FF0000",
			OwnerUserID:     "owner123",
			Members: []league.LeagueMember{
				{
					UserID:                 userID,
					UserName:               "Test User",
					PhotoURL:               "https://example.com/photo.jpg",
					JoinedAt:               time.Now().AddDate(0, 0, -30),
					TransactionStreak:      5,
					CurrentLeagueLevel:     league.BronzeLevel,
					LastStreakActivityDate: time.Date(2024, 1, 13, 0, 0, 0, 0, time.UTC),
				},
			},
			CurrentSeason: league.Season{
				StartDate: time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
				EndDate:   time.Date(2024, 12, 31, 0, 0, 0, 0, time.UTC),
			},
			LevelRequirements: []league.LevelRequirement{
				{Level: league.BronzeLevel, TransactionStreakNeeded: 0, Emoji: "🥉", Color: "#CD7F32", Description: "Bronze Tier"},
				{Level: league.SilverLevel, TransactionStreakNeeded: 5, Emoji: "🥈", Color: "#C0C0C0", Description: "Silver Tier"},
				{Level: league.GoldLevel, TransactionStreakNeeded: 10, Emoji: "🥇", Color: "#FFD700", Description: "Gold Tier"},
				{Level: league.DiamondLevel, TransactionStreakNeeded: 15, Emoji: "💎", Color: "#B9F2FF", Description: "Diamond Tier"},
			},
		},
		{
			ObjectID:        primitive.NewObjectID(),
			ID:              "league2",
			Name:            "Integration League 2",
			BackgroundColor: "#00FF00",
			OwnerUserID:     "owner123",
			Members: []league.LeagueMember{
				{
					UserID:                 userID,
					UserName:               "Test User",
					PhotoURL:               "https://example.com/photo.jpg",
					JoinedAt:               time.Now().AddDate(0, 0, -30),
					TransactionStreak:      9,
					CurrentLeagueLevel:     league.SilverLevel,
					LastStreakActivityDate: time.Date(2024, 1, 13, 0, 0, 0, 0, time.UTC),
				},
			},
			CurrentSeason: league.Season{
				StartDate: time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
				EndDate:   time.Date(2024, 12, 31, 0, 0, 0, 0, time.UTC),
			},
			LevelRequirements: []league.LevelRequirement{
				{Level: league.BronzeLevel, TransactionStreakNeeded: 0, Emoji: "🥉", Color: "#CD7F32", Description: "Bronze Tier"},
				{Level: league.SilverLevel, TransactionStreakNeeded: 5, Emoji: "🥈", Color: "#C0C0C0", Description: "Silver Tier"},
				{Level: league.GoldLevel, TransactionStreakNeeded: 10, Emoji: "🥇", Color: "#FFD700", Description: "Gold Tier"},
				{Level: league.DiamondLevel, TransactionStreakNeeded: 15, Emoji: "💎", Color: "#B9F2FF", Description: "Diamond Tier"},
			},
		},
	}
}
